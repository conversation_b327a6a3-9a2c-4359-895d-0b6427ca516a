# zendkee_customize

WordPress自定义插件，提供各种网站定制功能，包括强大的URL替换工具。

## 功能特性

### 🔧 核心功能
- **智能URL替换工具** - 支持数据库和文件的批量URL替换
- **网站访问控制** - 基于地理位置的访问控制
- **自定义设置面板** - 直观的管理界面
- **多语言支持** - 中文界面和注释

### 🚀 URL替换工具亮点
- **精准智能猜测** - 从2个URL优化到10个精准相关的URL
- **优先级排序** - 最可能的旧URL排在第一位
- **多数据源支持** - 数据库、配置文件、环境变量
- **批量处理** - 支持多站点批量操作
- **安全可靠** - 完整的错误处理和日志记录

## 📁 项目结构

```
zendkee_customize/
├── include/                    # 核心功能文件
│   ├── functions.php          # DomainNameChanger类和辅助函数
│   ├── settings-update-online.php  # URL替换后端处理
│   ├── settings-view-online.php    # URL替换前端界面
│   └── settings-update-misc.php    # 原项目的URL猜测逻辑
├── js/
│   └── admin.js              # 前端JavaScript，包含URL猜测逻辑
├── css/                      # 样式文件
├── vendor/                   # 第三方库
├── wp_url_replacer.php       # 🆕 PHP版本的URL替换工具
├── batch_replace.php         # 🆕 批量处理脚本
├── interactive_url_replacer.php  # 🆕 统一交互式URL替换脚本
├── example_usage.php         # 🆕 使用示例
├── backups/                  # 🆕 自动备份目录
├── operation_history.json    # 🆕 操作历史记录
├── interactive_replacer.log  # 🆕 交互式脚本日志
└── change.log               # 详细更新日志
```

## 🛠️ 安装使用

### WordPress插件安装
1. 将插件文件夹上传到 `/wp-content/plugins/` 目录
2. 在WordPress后台激活插件
3. 在设置菜单中找到"zendkee自定义"选项

### 独立URL替换工具使用

#### 🆕 统一交互式模式（强烈推荐）
```bash
# 启动统一的交互式URL替换脚本
php interactive_url_replacer.php
```

**功能特性：**
- 🔍 **智能站点发现**：自动扫描并列出WordPress站点
- 🎯 **精准URL猜测**：基于数据库、服务器环境智能猜测旧URL
- 💾 **自动备份**：操作前可选的数据库备份
- 📝 **详细日志**：完整的操作记录和历史管理
- 🔄 **回滚支持**：基于备份的完整回滚功能
- 🎨 **友好界面**：彩色输出和清晰的用户交互

#### 传统交互式模式
```bash
php wp_url_replacer.php /var/www/dev_sites/your-site
```

#### 命令行模式
```bash
# 完整替换
php wp_url_replacer.php /path/to/wordpress --old-url="http://old.com" --new-url="https://new.com"

# 只替换数据库
php wp_url_replacer.php /path/to/wordpress --old-url="http://old.com" --new-url="https://new.com" --db-only

# 只替换文件
php wp_url_replacer.php /path/to/wordpress --old-url="http://old.com" --new-url="https://new.com" --files-only
```

#### 批量处理
```bash
# 列出所有WordPress站点
php batch_replace.php --list

# 批量替换多个站点
php batch_replace.php --sites="site1,site2" --old-pattern="http://localhost/{site}" --new-pattern="https://{site}.com"
```

## 🧠 智能URL猜测功能

### 原项目逻辑
- 从数据库获取siteurl选项
- 通过外部服务获取服务器IP
- **结果：通常2个URL**

### 改进后的精准智能猜测
基于用户反馈重新设计，注重精准性而非完整性：

1. **优先级系统**
   - 数据库siteurl/home：优先级最高
   - 服务器IP：高优先级
   - 本地开发环境：中等优先级
   - 测试域名：较低优先级

2. **相关性过滤**
   - 排除API、文档、社交媒体URL
   - 只保留包含站点名的URL
   - 过滤本地开发环境相关URL
   - 智能识别项目相关域名

3. **精选URL类型**
   - 数据库配置URL（最重要）
   - 服务器IP地址
   - 常见本地开发模式
   - 相关测试域名

### **结果：10个精准相关的URL！**

**实际测试结果：**
1. `https://preview.yhct.site/vincetest` ← 数据库中的真实URL
2. `http://localhost/vincetest` ← 最常见开发环境
3. `http://**************` ← 服务器IP
4. 其他7个相关的开发/测试URL

## 📊 测试结果

测试站点：`/var/www/dev_sites/vincetest`

**发现的重要URL：**
- 🌐 生产环境：`https://preview.yhct.site/vincetest`
- 🖥️ 服务器IP：`http://**************`
- 💻 本地开发：`http://localhost/vincetest`
- 🧪 测试域名：`http://vincetest.local`

涵盖了从开发到生产的各个阶段！

## 🚀 统一交互式脚本详细使用指南

### 启动脚本
```bash
php interactive_url_replacer.php
```

### 使用流程

#### 1. 站点发现阶段
- 脚本启动后会显示欢迎界面
- 提示输入WordPress站点根目录路径（默认：/var/www/dev_sites/）
- 支持添加多个扫描目录
- 自动扫描并显示发现的WordPress站点列表

#### 2. 站点选择阶段
- 显示所有发现的WordPress站点
- 每个站点显示名称和完整路径
- 用户选择要处理的目标站点

#### 3. URL猜测与选择阶段
- 系统智能分析站点并猜测可能的旧URL
- 显示猜测结果，包括URL来源和优先级
- 提供"自定义输入"选项
- 用户选择旧URL或手动输入
- 输入新的目标URL

#### 4. 操作确认阶段
- 显示完整的操作预览：
  - 站点信息（名称、路径）
  - URL替换信息（旧URL → 新URL）
  - 操作范围（数据库 + 文件）
- 提供数据库备份选项
- 最终确认执行操作

#### 5. 执行阶段
- 实时显示操作进度
- 记录详细的操作日志
- 保存操作历史到JSON文件

#### 6. 完成后选项
- 处理另一个站点
- 查看操作历史
- 回滚最近的操作
- 退出程序

### 高级功能

#### 操作历史管理
- 所有操作自动记录到 `operation_history.json`
- 包含时间戳、站点信息、URL变更、备份文件等
- 可随时查看历史操作详情

#### 回滚功能
- 自动识别可回滚的操作（需要备份文件）
- 提供历史操作列表供选择
- 安全的数据库恢复流程
- 回滚前的详细确认

#### 备份管理
- 操作前可选的自动数据库备份
- 备份文件保存到 `backups/` 目录
- 文件命名格式：`站点名_backup_时间戳.sql`
- 备份状态验证和错误处理

### 安全特性
- **多重确认**：操作前的详细预览和确认
- **输入验证**：严格的URL格式和路径验证
- **自动备份**：可选的数据库备份保护
- **回滚支持**：基于备份的完整恢复能力
- **详细日志**：完整的操作记录和错误追踪

## 🔒 安全特性

- **数据安全**：事务处理，每次只更新一条记录
- **文件安全**：自动跳过二进制文件和系统目录
- **错误处理**：完整的异常处理和日志记录
- **备份建议**：使用前务必备份数据库和文件

## 🎯 使用场景

- **网站迁移**：本地 → 测试 → 生产环境
- **域名更换**：更换网站域名
- **协议升级**：HTTP → HTTPS
- **批量管理**：多站点统一管理

## 📝 更新日志

### v2.0 (2025-01-17) - 统一交互式URL替换脚本 🆕
- 🎯 **全新统一脚本**：创建interactive_url_replacer.php整合所有功能
- 🔍 **智能站点发现**：自动扫描WordPress站点，支持多目录输入
- 🎨 **友好用户界面**：彩色输出、清晰布局、实时进度显示
- 💾 **完整备份支持**：操作前自动数据库备份，支持完整回滚
- 📝 **详细历史管理**：JSON格式操作历史，可查看和回滚任意操作
- 🔒 **增强安全性**：多重确认、输入验证、错误处理
- 🔄 **回滚功能**：基于备份文件的完整数据库回滚支持

### v1.2 (2024-07-16) - 精准智能猜测
- 🎯 重新设计URL猜测逻辑，从48个混乱URL优化到10个精准URL
- 📊 实现优先级系统和相关性过滤
- 🥇 最重要的URL（数据库真实URL）排在第一位
- 👥 解决用户体验问题，提供真正实用的功能

### v1.1 (2024-07-16) - 功能扩展
- ✨ 创建PHP版本的独立URL替换工具
- 🚀 智能URL猜测功能大幅增强
- 📦 支持批量处理多个站点
- 🔍 多数据源URL提取
- 🛡️ 增强的安全和错误处理

### v1.0 - 原始版本
- 基础URL替换功能
- WordPress插件集成
- 简单的URL猜测

详细更新日志请查看 `change.log` 文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目仅供学习和内部使用，请遵守相关法律法规。
